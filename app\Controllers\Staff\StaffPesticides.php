<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmPesticidesDataModel;
use App\Models\PesticidesModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\CropsModel;
use App\Models\usersModel;

class StaffPesticides extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmPesticidesData':
                    $this->models[$modelName] = new CropsFarmPesticidesDataModel();
                    break;
                case 'pesticides':
                    $this->models[$modelName] = new PesticidesModel();
                    break;
                case 'farmBlocks':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new AdxDistrictModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new AdxProvinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new AdxLlgModel();
                    break;
                case 'wards':
                    $this->models[$modelName] = new AdxWardModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmPesticidesDataModel() { return $this->getModel('farmPesticidesData'); }
    protected function getPesticidesModel() { return $this->getModel('pesticides'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlocks'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getWardModel() { return $this->getModel('wards'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getUsersModel() { return $this->getModel('users'); }

    protected function verifyDistrictAccess($districtId)
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    public function pesticides_data()
    {
        $district = $this->getDistrictModel()->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Pesticides Data',
            'page_header' => 'Pesticides Data',
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'pesticides' => $this->getPesticidesModel()->findAll()
        ];

        return view('staff/farms/pesticides_data', $data);
    }

    public function view_pesticides_data($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Pesticides Data',
            'page_header' => 'Block Pesticides Data',
            'block' => $block,
            'pesticides_data' => $this->getFarmPesticidesDataModel()->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('action_date', 'DESC')
                ->findAll(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getProvinceModel()->find($block['province_id']),
            'district' => $this->getDistrictModel()->find($block['district_id']),
            'llg' => $this->getLlgModel()->find($block['llg_id']),
            'ward' => $this->getWardModel()->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'pesticides' => $this->getPesticidesModel()->findAll(),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_pesticides_data', $data);
    }

    public function add_pesticides_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'block_id' => $block_id,
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmPesticidesDataModel()->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_pesticides_data()
    {
        try {
            $id = $this->request->getPost('id');
            $pesticides_data = $this->getFarmPesticidesDataModel()->find($id);

            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmPesticidesDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_pesticides_data($id)
    {
        try {
            $pesticides_data = $this->getFarmPesticidesDataModel()->find($id);

            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmPesticidesDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
}
