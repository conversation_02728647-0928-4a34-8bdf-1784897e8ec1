<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\AdxDistrictModel;
use App\Models\FarmerInformationModel;
use App\Models\AdxLlgModel;
use App\Models\AdxProvinceModel;
use App\Models\usersModel;
use App\Models\AdxWardModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\LivestockFarmDataModel;
use App\Models\LivestockModel;

class Staff_Livestock extends BaseController
{
    protected $usersModel;
    protected $farmersModel;
    protected $provincesModel;
    protected $districtsModel;
    protected $llgsModel;
    protected $wardsModel;
    protected $livestockFarmBlockModel;
    protected $livestockFarmDataModel;
    protected $livestockModel;

    public function __construct()
    {
        helper(['url', 'form', 'info', 'weather']);
        $this->usersModel = new usersModel();
        $this->farmersModel = new FarmerInformationModel();
        $this->provincesModel = new AdxProvinceModel();
        $this->districtsModel = new AdxDistrictModel();
        $this->llgsModel = new AdxLlgModel();
        $this->wardsModel = new AdxWardModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->livestockFarmDataModel = new LivestockFarmDataModel();
        $this->livestockModel = new LivestockModel();
    }

    public function farm_blocks()
    {
        $data = [
            'title' => 'Livestock Farm Blocks',
            'page_header' => 'Livestock Farm Blocks',
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'districts' => $this->districtsModel->where('province_id', session()->get('orgprovince_id'))->findAll(),
            'farm_blocks' => $this->livestockFarmBlockModel
                ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname')
                ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
                ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
                ->where('livestock_farm_blocks.status !=', 'deleted')
                ->orderBy('livestock_farm_blocks.id', 'asc')
                ->findAll()
        ];

        return view('staff/livestock/livestock_farm_block', $data);
    }

    public function get_farm_blocks()
    {
        try {
            $farm_blocks = $this->livestockFarmBlockModel
                ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname')
                ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
                ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
                ->where('livestock_farm_blocks.status !=', 'deleted')
                ->orderBy('livestock_farm_blocks.id', 'asc')
                ->findAll();

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $farm_blocks
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Get Farm Blocks] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to retrieve farm blocks'
            ]);
        }
    }

    public function get_llgs()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $districtId = $this->request->getPost('district_id');
        if (!$districtId) {
            return $this->response->setJSON(['success' => false, 'message' => 'District ID is required']);
        }

        try {
            $llgs = $this->llgsModel
                ->where('district_id', $districtId)
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'llgs' => $llgs
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_llgs: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error occurred'
            ]);
        }
    }

    public function get_wards()
    {
        if ($this->request->isAJAX()) {
            $llg_id = $this->request->getPost('llg_id');

            if (!$llg_id) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'LLG ID is required',
                    'wards' => []
                ]);
            }

            try {
                $wards = $this->wardsModel->where('llg_id', $llg_id)->findAll();

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Wards retrieved successfully',
                    'wards' => $wards
                ]);
            } catch (\Exception $e) {
                log_message('error', 'Error getting wards: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Database error occurred',
                    'wards' => []
                ]);
            }
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Invalid request',
            'wards' => []
        ]);
    }

    public function add_farm_block()
    {
        try {
            //get farmer id 
            $farmer_id = $this->request->getPost('farmer_id');

            //get the latest block code of this farmer
            $latest_block_code = $this->livestockFarmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();

            //get farmer code
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];
            
            //generate the next block code
            if ($latest_block_code) {
                // Extract everything after the dash and convert to integer
                $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                // Increment by 1 and format with leading zeros (3 digits)
                $next_block_code = 'LS' . $farmer_code . '-' . sprintf('%03d', $current_number + 1);
            } else {
                // First block code starts with 001
                $next_block_code = 'LS' . $farmer_code . '-001';
            }

            $data = [
                'farmer_id' => $farmer_id,
                'block_code' => $next_block_code,
                'org_id' => session()->get('org_id'),
                'country_id' => session()->get('orgcountry_id'),
                'province_id' => session()->get('orgprovince_id'),
                'district_id' => $this->request->getPost('district_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            $this->livestockFarmBlockModel->save($data);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Farm block added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Farm Block] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An error occurred while adding the farm block'
            ]);
        }
    }

    public function update_farm_block()
    {
        try {
            if (!$this->request->isAJAX()) {
                throw new \Exception('Invalid request');
            }

            // Get block ID from the form
            $block_id = $this->request->getPost('block_id');

            // Verify block exists and belongs to the current staff member
            $existing_block = $this->livestockFarmBlockModel
                ->where('id', $block_id)
                ->where('created_by', session()->get('emp_id'))
                ->first();

            if (!$existing_block) {
                throw new \Exception('Farm block not found or access denied');
            }

            //get farmer id
            $farmer_id = $this->request->getPost('farmer_id');

            //get farmer code
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];

            //if block before the dash is not the same as the farmer code 
            if (substr($existing_block['block_code'], 0, strpos($existing_block['block_code'], '-')) !== $farmer_code) {
                //get the latest block code of this farmer
                $latest_block_code = $this->livestockFarmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();

                //generate the next block code
                if ($latest_block_code) {
                    // Extract everything after the dash and convert to integer
                    $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                    // Increment by 1 and format with leading zeros (3 digits)
                    $next_block_code = 'LS' . $farmer_code . '-' . sprintf('%03d', $current_number + 1);
                } else {
                    // First block code starts with 001
                    $next_block_code = 'LS' . $farmer_code . '-001';
                }
            } else {
                $next_block_code = $existing_block['block_code'];
            }

            // Prepare update data
            $data = [
                'block_code' => $next_block_code,
                'farmer_id' => $this->request->getPost('farmer_id'),
                'province_id' => session('orgprovince_id'),
                'district_id' => $this->request->getPost('district_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'status' => $this->request->getPost('status'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update the farm block
            $this->livestockFarmBlockModel->update($block_id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Farm block updated successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Farm Block] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_farm_block()
    {
        try {
            // Get block ID from the request
            $id = $this->request->getPost('id');

            // Verify block exists and belongs to the current staff member
            $existing_block = $this->livestockFarmBlockModel
                ->where('id', $id)
                ->where('created_by', session()->get('emp_id'))
                ->first();

            if (!$existing_block) {
                throw new \Exception('Farm block not found or access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->livestockFarmBlockModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Farm block deleted successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Farm Block] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function farm_data()
    {
        $data = [
            'title' => 'Livestock Farm Data',
            'page_header' => 'Livestock Farm Data',
            'farm_blocks' => $this->livestockFarmBlockModel->getFarmBlocksWithDetails()
        ];

        return view('staff/livestock/livestock_data', $data);
    }

    public function view_farm_data($block_id)
    {
        $block = $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname,
                    adx_ward.name as ward_name, adx_llg.name as llg_name,
                    adx_district.name as district_name, adx_province.name as province_name')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
            ->where('livestock_farm_blocks.id', $block_id)
            ->first();

        if (!$block) {
            return redirect()->back()->with('error', 'Block not found');
        }

        $data = [
            'title' => 'Block Livestock Data',
            'page_header' => 'Block Livestock Data',
            'block' => $block,
            'livestock_types' => $this->livestockModel->orderBy('livestock_name', 'ASC')->findAll(),
            'livestock_data' => $this->livestockFarmDataModel
                ->select('livestock_farm_data.*, adx_livestock.livestock_name')
                ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
                ->where('block_id', $block_id)
                ->where('livestock_farm_data.status !=', 'deleted')
                ->findAll()
        ];

        return view('staff/livestock/view_livestock_farm_data', $data);
    }

    public function add_livestock_data()
    {
        try {
            $data = [
                'block_id' => $this->request->getPost('block_id'),
                'livestock_id' => $this->request->getPost('livestock_id'),
                'breed' => $this->request->getPost('breed'),
                'he_total' => $this->request->getPost('he_total'),
                'she_total' => $this->request->getPost('she_total'),
                'pasture_type' => $this->request->getPost('pasture_type'),
                'growth_stage' => $this->request->getPost('growth_stage'),
                'cost_per_livestock' => $this->request->getPost('cost_per_livestock'),
                'low_price_per_livestock' => $this->request->getPost('low_price_per_livestock'),
                'high_price_per_livestock' => $this->request->getPost('high_price_per_livestock'),
                'comments' => $this->request->getPost('comments'),
                'action_date' => $this->request->getPost('action_date'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = [
                'block_id', 'livestock_id', 'breed', 'he_total', 'she_total', 
                'pasture_type', 'growth_stage', 'cost_per_livestock', 
                'low_price_per_livestock', 'high_price_per_livestock', 'action_date'
            ];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception("The {$field} field is required.");
                }
            }

            // Validate numeric fields
            if (!is_numeric($data['he_total']) || $data['he_total'] < 0) {
                throw new \Exception("Male total must be a non-negative number.");
            }
            if (!is_numeric($data['she_total']) || $data['she_total'] < 0) {
                throw new \Exception("Female total must be a non-negative number.");
            }
            if (!is_numeric($data['cost_per_livestock']) || $data['cost_per_livestock'] < 0) {
                throw new \Exception("Cost per livestock must be a non-negative number.");
            }
            if (!is_numeric($data['low_price_per_livestock']) || $data['low_price_per_livestock'] < 0) {
                throw new \Exception("Lowest price must be a non-negative number.");
            }
            if (!is_numeric($data['high_price_per_livestock']) || $data['high_price_per_livestock'] < 0) {
                throw new \Exception("Highest price must be a non-negative number.");
            }
            if ($data['low_price_per_livestock'] > $data['high_price_per_livestock']) {
                throw new \Exception("Lowest price cannot be greater than highest price.");
            }

            $this->livestockFarmDataModel->insert($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Livestock data added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Livestock Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_livestock_data()
    {
        try {
            $id = $this->request->getPost('id');
            $data = [
                'livestock_id' => $this->request->getPost('livestock_id'),
                'breed' => $this->request->getPost('breed'),
                'he_total' => $this->request->getPost('he_total'),
                'she_total' => $this->request->getPost('she_total'),
                'pasture_type' => $this->request->getPost('pasture_type'),
                'growth_stage' => $this->request->getPost('growth_stage'),
                'cost_per_livestock' => $this->request->getPost('cost_per_livestock'),
                'low_price_per_livestock' => $this->request->getPost('low_price_per_livestock'),
                'high_price_per_livestock' => $this->request->getPost('high_price_per_livestock'),
                'comments' => $this->request->getPost('comments'),
                'action_date' => $this->request->getPost('action_date'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = [
                'livestock_id', 'breed', 'he_total', 'she_total', 
                'pasture_type', 'growth_stage', 'cost_per_livestock', 
                'low_price_per_livestock', 'high_price_per_livestock', 'action_date'
            ];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception("The {$field} field is required.");
                }
            }

            // Validate numeric fields
            if (!is_numeric($data['he_total']) || $data['he_total'] < 0) {
                throw new \Exception("Male total must be a non-negative number.");
            }
            if (!is_numeric($data['she_total']) || $data['she_total'] < 0) {
                throw new \Exception("Female total must be a non-negative number.");
            }
            if (!is_numeric($data['cost_per_livestock']) || $data['cost_per_livestock'] < 0) {
                throw new \Exception("Cost per livestock must be a non-negative number.");
            }
            if (!is_numeric($data['low_price_per_livestock']) || $data['low_price_per_livestock'] < 0) {
                throw new \Exception("Lowest price must be a non-negative number.");
            }
            if (!is_numeric($data['high_price_per_livestock']) || $data['high_price_per_livestock'] < 0) {
                throw new \Exception("Highest price must be a non-negative number.");
            }
            if ($data['low_price_per_livestock'] > $data['high_price_per_livestock']) {
                throw new \Exception("Lowest price cannot be greater than highest price.");
            }

            $this->livestockFarmDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Livestock data updated successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Livestock Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_livestock_data()
    {
        try {
            $id = $this->request->getPost('id');
            
            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->livestockFarmDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Livestock data deleted successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Livestock Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
}
