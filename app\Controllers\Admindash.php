<?php

namespace App\Controllers;

use App\Models\FarmerInformationModel;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxLlgModel;
use App\Models\AdxCountryModel;
use App\Models\AdxWardModel;

class Admindash extends BaseController
{
    protected $farmerModel;
    protected $buyerModel;
    protected $blockModel;
    protected $farmCropsModel;
    protected $fertilizerModel;
    protected $pesticideModel;
    protected $harvestModel;
    protected $marketingModel;
    protected $cropsModel;
    protected $districtModel;
    protected $provinceModel;
    protected $llgModel;
    protected $countryModel;
    protected $wardModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        
        $this->farmerModel = new FarmerInformationModel();
        $this->buyerModel = new CropBuyersModel();
        $this->blockModel = new CropsFarmBlockModel();
        $this->farmCropsModel = new CropsFarmCropsDataModel();
        $this->fertilizerModel = new CropsFarmFertilizerDataModel();
        $this->pesticideModel = new CropsFarmPesticidesDataModel();
        $this->harvestModel = new CropsFarmHarvestDataModel();
        $this->marketingModel = new CropsFarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
        $this->districtModel = new AdxDistrictModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->llgModel = new AdxLlgModel();
        $this->countryModel = new AdxCountryModel();
        $this->wardModel = new AdxWardModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Admin Dashboard',
            'page_header' => 'Dashboard',
            'page_desc' => 'Overview of Agricultural Statistics',
        ];

        // Set active menu
        $data['menu'] = 'dashboard';

        // Basic Statistics
        $data['total_farmers'] = $this->farmerModel->countAll();
        $data['total_buyers'] = $this->buyerModel->countAll();
        $data['total_farm_blocks'] = $this->blockModel->countAll();

        // Crop Statistics
        $data['crops_data'] = [
            'total_plantings' => $this->farmCropsModel->countAll(),
            'active_plantings' => $this->farmCropsModel->where('status', 'active')->countAllResults()
        ];

        // Harvest Statistics
        $data['harvest_data'] = [
            'total_harvests' => $this->harvestModel->countAll(),
            'total_quantity' => $this->harvestModel->selectSum('quantity')->get()->getRow()->quantity ?? 0
        ];

        // Marketing Statistics
        $marketingStats = $this->marketingModel
            ->select('SUM(market_price_per_unit * quantity) as total_sales, SUM(quantity) as total_sold_quantity')
            ->get()
            ->getRow();
            
        $data['marketing_data'] = [
            'total_sales' => $marketingStats->total_sales ?? 0,
            'total_sold_quantity' => $marketingStats->total_sold_quantity ?? 0
        ];

        // Recent Activity
        $data['recent_harvests'] = $this->harvestModel->orderBy('created_at', 'DESC')->limit(5)->find();
        $data['recent_sales'] = $this->marketingModel->orderBy('created_at', 'DESC')->limit(5)->find();

        // Geographical Statistics
        $data['total_provinces'] = $this->provinceModel->countAll();
        $data['total_districts'] = $this->districtModel->countAll();
        $data['total_llgs'] = $this->llgModel->countAll();
        $data['total_wards'] = $this->wardModel->countAll();

        // Top Crops
        $data['top_crops'] = $this->farmCropsModel->select('crop_id, COUNT(*) as count')
            ->groupBy('crop_id')
            ->orderBy('count', 'DESC')
            ->limit(5)
            ->find();
        
        // Get crop types for reference
    //    $data['crop_types'] = $this->cropsModel->where('box', 'crops')->find();

        // Organization Info (for license status)
        $data['org'] = [
            'license_status' => 'active' // You may want to get this from a settings model
        ];

        return view('admindash/dashboard', $data);
    }
}
